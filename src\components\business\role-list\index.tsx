import clsx from "clsx";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import { Card } from "../card";
import EmptyState, { EmptyType } from "../empty";

const RoleList = ({ onCreateRole }: { onCreateRole: () => void }) => {
  const { t } = useLanguage();

  const { roleList, currentRole, setCurrentRole, setIsDefault } =
    useRoleStore();
  if (roleList.length === 0) {
    return (
      <EmptyState
        iconSize="sm"
        compact
        icon={<ProfileIcon seed="no-roles" size={40}></ProfileIcon>}
        type={EmptyType.NO_DATA}
        title={t("components:empty.noRoles")}
        description={t("components:empty.description.noRoles")}
        actions={[
          {
            label: t("components:empty.actions.create"),
            onClick: () => {
              onCreateRole();
            },
            variant: "default",
          },
        ]}
      />
    );
  }
  return (
    <div className="flex flex-col gap-2 py-4 pr-2.5">
      {roleList.map((role) => (
        <Card
          key={role.personaId}
          className={clsx(
            "cursor-pointer bg-background",
            role.personaId === currentRole.personaId
              ? "bg-primary-alpha-10 border-ring hover:bg-primary-alpha-10"
              : "hover:bg-surface-light"
          )}
        >
          <div
            className="flex gap-4 items-center"
            onClick={() => {
              setCurrentRole(role);
              setIsDefault(role.personaId);
            }}
          >
            <div className="w-10 h-10 rounded-full bg-surface-light flex items-center justify-center">
              <ProfileIcon seed={role.personaId} size={40}></ProfileIcon>
            </div>
            <div>
              <Text variant={TextVariant.BODY_MEDIUM}>{role.name}</Text>
              <Text
                variant={TextVariant.BODY_SMALL}
                className="text-text-secondary mt-1"
              >
                {role.mbti}
              </Text>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default RoleList;
