import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  personaCreate<PERSON><PERSON>,
  IPersona,
  personaList<PERSON>pi,
  personaSetDefaultApi,
  personaUpdateApi,
} from "@/services/api/persona";

interface RoleStore {
  roleList: IPersona[];
  roleForm: IPersona;
  currentRole: IPersona;
  setCurrentRole: (role: IPersona) => void;
  createRole: (data: IPersona) => Promise<void>;
  getRoleList: () => Promise<void>;
  setIsDefault: (personaId: string) => Promise<void>;
  updateRole: (data: IPersona) => Promise<void>;
}
const useRoleStore = create<RoleStore>()(
  immer(
    (set, get): RoleStore => ({
      roleForm: {} as IPersona,
      roleList: [],
      currentRole: {} as IPersona,
      setCurrentRole: (role: IPersona) => {
        set({
          currentRole: role,
        });
      },
      createRole: async (data: IPersona) => {
        await personaCreateApi(data);
        await get().getRoleList();
      },
      getRoleList: async () => {
        const res = await personaListApi();
        if (res.success) {
          const personas = res.data?.personas || [];
          const defaultRole = personas.filter((p) => p.isDefault)[0];

          set({
            roleList: personas,
            currentRole: defaultRole || personas[0] || ({} as IPersona), // 如果没有默认角色，使用第一个角色，如果没有角色则使用空对象
          });
        }
      },
      setIsDefault: async (personaId: string) => {
        await personaSetDefaultApi(personaId);
        await get().getRoleList();
      },
      updateRole: async (data: IPersona) => {
        await personaUpdateApi(data);
        await get().getRoleList();
      },
    })
  )
);

export default useRoleStore;
