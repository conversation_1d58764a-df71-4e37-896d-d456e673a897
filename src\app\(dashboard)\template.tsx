"use client";

import { motion } from "framer-motion";
// import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { RouteErrorBoundary } from "@/components/RouteErrorBoundary";
import { validateTokenApi } from "@/services/api/user";
export default function Template({ children }: { children: React.ReactNode }) {
  const validateToken = async () => {
    try {
      const res = await validateTokenApi();
      if (!res.success) {
        if (typeof window !== "undefined") {
          localStorage.clear();
          window.location.href = "/login";
        }
      }
    } catch (error) {
      console.error(error);
      if (typeof window !== "undefined") {
        localStorage.clear();
        window.location.href = "/login";
      }
    }
  };
  useEffect(() => {
    validateToken();
  }, []);
  return (
    <RouteErrorBoundary>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="h-full"
      >
        {children}
      </motion.div>
    </RouteErrorBoundary>
  );
}
