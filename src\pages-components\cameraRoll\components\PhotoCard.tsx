"use client";

import clsx from "clsx";
import { Loader } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Card } from "@/components/business/card";
import { ImageModal } from "@/components/business/upload/components/ImageModal";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/hooks/useLanguage";
import { cn } from "@/lib/utils";
import { IPhoto } from "@/services/api/photo";

interface PhotoCardProps {
  photo: IPhoto;
  className?: string;
}

const PhotoCard = ({ photo, className }: PhotoCardProps) => {
  const [showPreview, setShowPreview] = useState(false);
  const { t } = useLanguage();

  let summary = {
    content: "",
    tags: [],
    type: "",
  };
  try {
    summary = JSON.parse(photo.summary);
  } catch (error) {
    summary = {
      content: "",
      tags: [],
      type: "",
    };
    console.error("Failed to parse photo summary:", error);
  }

  return (
    <>
      <Card
        className={cn(
          "hover:bg-surface-light hover:shadow-glow-lg transition-all duration-200 py-2",
          className
        )}
        classContentName="px-2"
      >
        <div className="cursor-pointer" onClick={() => setShowPreview(true)}>
          <div className="relative w-full h-32 sm:h-36 md:h-40 lg:h-44 xl:h-48 overflow-hidden rounded-t-lg">
            <Image
              src={photo.imageUrl}
              alt={photo.fileName}
              fill
              className="object-cover transition-transform hover:scale-105"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, (max-width: 1536px) 20vw, 16vw"
              priority={false}
            />
          </div>
          <div className="flex flex-col mt-2 gap-2 sm:gap-3 p-2 sm:p-3">
            <div className="text-xs sm:text-sm text-text-secondary truncate font-medium">
              {photo.fileName}
            </div>
            {summary.content ? (
              <div>
                {photo.summary && (
                  <div
                    className="text-xs text-text-tertiary line-clamp-2 leading-relaxed hidden sm:block"
                    title={summary.content}
                  >
                    {summary.content}
                  </div>
                )}
                <div className="flex items-center justify-between text-xs text-text-tertiary">
                  <span>{new Date(photo.createdAt).toLocaleDateString()}</span>
                  <span
                    className={clsx(
                      "px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full text-xs",
                      photo.isUse
                        ? "text-primary bg-primary-alpha-10"
                        : "text-text-tertiary bg-surface-lighter"
                    )}
                  >
                    {photo.isUse
                      ? t("camera:photoAnalyzer.statistics.used")
                      : t("camera:photoAnalyzer.statistics.unused")}
                  </span>
                </div>
                {/* 预留标签位置 */}
                <div className="flex gap-2 mt-2 flex-wrap">
                  {summary.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <div className="mt-2">
                  <Badge variant="default">{summary.type}</Badge>
                </div>
              </div>
            ) : (
              <div className="text-text-tertiary flex justify-center items-center flex-col">
                <Loader className="size-12 spin360 mt-8 mb-4"></Loader>
                <span>{t("camera:photoAnalyzer.AIAnalyzeTagsLoading")}</span>
              </div>
            )}
          </div>
        </div>
      </Card>

      {showPreview && (
        <ImageModal
          src={photo.imageUrl}
          alt={photo.fileName}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default PhotoCard;
