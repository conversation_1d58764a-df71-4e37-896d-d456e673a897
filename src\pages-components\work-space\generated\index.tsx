"use client";

import { ArrowLeftCircleIcon } from "@heroicons/react/24/outline";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { Card } from "@/components/business/card";
import { Button } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";
import { useAppStore } from "@/store/useAppStore";
import Twitter from "./components/Twitter";
import Xhs from "./components/Xhs";

const Generated = () => {
  const { t, currentLanguage } = useLanguage();
  const { setHeaderSlot } = useAppStore();
  const { currentRole } = useRoleStore();
  const {
    getResultByTaskId,
    finalContent,
    publish: publishToStore,
    save: saveToStore,
  } = useGenerateStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  useEffect(() => {
    setHeaderSlot(
      <div className="flex gap-2 items-center">
        <Button
          size="sm"
          onClick={() => router.replace("/workspace/generating")}
        >
          {t("button:regenerate")}
        </Button>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => router.replace("/workspace")}
        >
          <ArrowLeftCircleIcon className="size-4"></ArrowLeftCircleIcon>
          {t("button:backToWorkspace")}
        </Button>
      </div>
    );
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, router, t]);

  useEffect(() => {
    // In App Router, we need to get search params differently
    // For now, we'll handle this when we have proper routing setup
    const taskId = searchParams.get("taskId");
    if (taskId) {
      getResultByTaskId(taskId);
    }
  }, [searchParams]);
  const onPublish = async (data: IGenerateList) => {
    const taskId = searchParams.get("taskId");
    await publishToStore(data, taskId as string, currentRole.personaId);
  };
  const onSave = async (data: IGenerateList) => {
    const taskId = searchParams.get("taskId");
    await saveToStore(data, taskId as string, currentRole.personaId);
  };

  return (
    <div className="space-y-6">
      <Card className="border-primary-alpha-20 bg-primary-alpha-10">
        <div>
          <Text variant={TextVariant.H3} className="mb-4 text-primary">
            {t("workspace:generated.title", {
              num: finalContent.length,
              platform: t(
                currentLanguage === "zh-CN"
                  ? "workspace:generated.xhs"
                  : "workspace:generated.twitter"
              ),
            })}
          </Text>
          <Text
            variant={TextVariant.BODY_MEDIUM}
            className=" text-text-secondary"
          >
            {t("workspace:generated.tip", {
              platform: t(
                currentLanguage === "zh-CN"
                  ? "workspace:generated.xhs"
                  : "workspace:generated.twitter"
              ),
            })}
          </Text>
        </div>
      </Card>

      <div className="grid grid-cols-1 sm:grid-cols-[repeat(auto-fit,480px)] gap-4 justify-center">
        {finalContent.map((item, index) => {
          // 根据选择的平台渲染不同的组件
          const renderPlatformComponent = () => {
            switch (currentLanguage) {
              case "zh-CN":
                return (
                  <Xhs
                    key={index}
                    data={item}
                    onPublish={onPublish}
                    onSave={onSave}
                  ></Xhs>
                );
              // case "instagram":
              //   return (
              //     <Instagram
              //       key={index}
              //       data={item}
              //       onPublish={onPublish}
              //       onSave={onSave}
              //     />
              //   );
              // case "twitter":
              //   return (
              //     <Twitter
              //       key={index}
              //       data={item}
              //       onPublish={onPublish}
              //       onSave={onSave}
              //     />
              //   );
              default:
                return (
                  <Twitter
                    key={index}
                    data={item}
                    onPublish={onPublish}
                    onSave={onSave}
                  />
                );
            }
          };

          return renderPlatformComponent();
        })}
      </div>
    </div>
  );
};

export default Generated;
